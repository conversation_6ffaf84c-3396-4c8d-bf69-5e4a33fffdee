package com.tuowan.yeliao.commons.config.p6spy;

import com.easyooo.framework.common.util.StringUtils;
import com.p6spy.engine.spy.appender.MessageFormattingStrategy;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * P6spy SQL 打印策略
 *
 * <AUTHOR>
 * @since 2019-02-20
 */
public class P6SpyLogger implements MessageFormattingStrategy {

    private static String environmentName;

    // URL到数据库名称的映射缓存
    private static final Map<String, String> urlToDatabaseNameCache = new ConcurrentHashMap<>();

    // 数据库名称映射配置
    private static final Map<String, String> databaseMappings = new ConcurrentHashMap<>();

    static {
        // 尝试从系统属性或环境变量获取环境信息
        environmentName = getEnvironmentName();
        // 加载数据库映射配置
        loadDatabaseMappings();
    }

    /**
     * 加载数据库映射配置
     */
    private static void loadDatabaseMappings() {
        try {
            // 从classpath加载映射配置文件
            InputStream is = P6SpyLogger.class.getClassLoader()
                    .getResourceAsStream("p6spy-database-mapping.properties");
            if (is != null) {
                Properties props = new Properties();
                props.load(is);
                for (String key : props.stringPropertyNames()) {
                    databaseMappings.put(key.toLowerCase(), props.getProperty(key));
                }
                is.close();
            }
        } catch (Exception e) {
            // 如果加载失败，使用默认映射
            System.err.println("加载数据库映射配置失败，使用默认映射: " + e.getMessage());
        }

        // 添加默认映射（兜底方案）
        if (databaseMappings.isEmpty()) {
            databaseMappings.put("shanghai", "YL_CONFIG");
            databaseMappings.put("yl_acct", "YL_ACCT");
            databaseMappings.put("yl_user", "YL_USER");
            databaseMappings.put("yl_config", "YL_CONFIG");
            databaseMappings.put("yl_busi", "YL_BUSI");
            databaseMappings.put("yl_social", "YL_SOCIAL");
            databaseMappings.put("yl_log", "YL_LOG");
            databaseMappings.put("yl_cms", "YL_CMS");
            databaseMappings.put("yl_rep", "YL_REP");
        }
    }

    /**
     * 获取环境名称
     */
    private static String getEnvironmentName() {
        // 优先从spring.profiles.active获取
        String profiles = System.getProperty("spring.profiles.active");
        if (profiles == null || profiles.trim().isEmpty()) {
            profiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }

        if (profiles != null && !profiles.trim().isEmpty()) {
            // 直接返回第一个profile的大写形式，不做特殊映射
            return profiles.split(",")[0].trim().toUpperCase();
        }

        // 默认返回UNKNOWN
        return "UNKNOWN";
    }

    @Override
    public String formatMessage(int connectionId, String now, long elapsed, String category,
                                String prepared, String sql, String url) {
        if (StringUtils.isBlank(sql)) {
            return "";
        }

        // 清理SQL语句，去除多余空格和换行
        String cleanSql = sql.replaceAll("[\\s]+", " ").trim();

        // 获取环境名称
        String env = environmentName != null ? environmentName : "DEV";

        // 从URL中提取数据库名称
        String databaseName = extractDatabaseName(url, connectionId);

        // 获取调用栈信息
        String callerInfo = getCallerInfo();

        // 格式化日志：[SQL-DEV] 2025-07-04 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
        return String.format("[SQL-%s] %s | 执行时间: %dms | 数据库: %s | 调用: %s | SQL: %s",
                env, now, elapsed, databaseName, callerInfo, cleanSql);
    }

    /**
     * 从JDBC URL中提取数据库名称
     */
    private String extractDatabaseName(String url, int connectionId) {
        if (StringUtils.isBlank(url)) {
            return "UNKNOWN";
        }

        // 先检查缓存
        String cachedName = urlToDatabaseNameCache.get(url);
        if (cachedName != null) {
            return cachedName;
        }

        try {
            // 处理p6spy包装的URL：*************************************************
            String actualUrl = url;
            if (url.startsWith("jdbc:p6spy:")) {
                actualUrl = url.substring("jdbc:p6spy:".length());
                if (!actualUrl.startsWith("jdbc:")) {
                    actualUrl = "jdbc:" + actualUrl;
                }
            }

            // 从URL中提取数据库名称
            // 支持格式：******************************************************************************
            if (actualUrl.contains("/") && actualUrl.contains("://")) {
                String[] parts = actualUrl.split("://");
                if (parts.length > 1) {
                    String hostAndDb = parts[1];
                    // 找到最后一个/后面的数据库名
                    int lastSlash = hostAndDb.lastIndexOf('/');
                    if (lastSlash >= 0 && lastSlash < hostAndDb.length() - 1) {
                        String dbPart = hostAndDb.substring(lastSlash + 1);

                        // 去掉URL参数（? 或 & 开始的部分）
                        int questionMark = dbPart.indexOf('?');
                        if (questionMark >= 0) {
                            dbPart = dbPart.substring(0, questionMark);
                        }
                        int ampersand = dbPart.indexOf('&');
                        if (ampersand >= 0) {
                            dbPart = dbPart.substring(0, ampersand);
                        }

                        // 使用映射配置转换数据库名称
                        String logicalDbName = mapToLogicalDatabaseName(dbPart.trim());

                        // 缓存结果
                        urlToDatabaseNameCache.put(url, logicalDbName);

                        return logicalDbName;
                    }
                }
            }
        } catch (Exception e) {
            // 如果解析失败，返回连接ID
            String fallbackName = "CONN-" + connectionId;
            urlToDatabaseNameCache.put(url, fallbackName);
            return fallbackName;
        }

        String fallbackName = "CONN-" + connectionId;
        urlToDatabaseNameCache.put(url, fallbackName);
        return fallbackName;
    }

    /**
     * 将物理数据库名映射为逻辑数据库名
     */
    private String mapToLogicalDatabaseName(String physicalDbName) {
        if (StringUtils.isBlank(physicalDbName)) {
            return "UNKNOWN";
        }

        String lowerDbName = physicalDbName.toLowerCase();

        // 1. 精确匹配
        String mapped = databaseMappings.get(lowerDbName);
        if (mapped != null) {
            return mapped;
        }

        // 2. 模糊匹配（包含关系）
        for (Map.Entry<String, String> entry : databaseMappings.entrySet()) {
            if (lowerDbName.contains(entry.getKey())) {
                return entry.getValue();
            }
        }

        // 3. 默认处理：直接转大写
        return physicalDbName.toUpperCase();
    }

    /**
     * 获取调用SQL的Java类、方法和行号信息
     */
    private String getCallerInfo() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();

            // 遍历调用栈，找到第一个非框架代码的调用点
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                String methodName = element.getMethodName();
                int lineNumber = element.getLineNumber();

                // 跳过框架相关的类
                if (isFrameworkClass(className)) {
                    continue;
                }

                // 找到业务代码，返回简化的类名.方法名:行号
                String simpleClassName = getSimpleClassName(className);
                return String.format("%s.%s:%d", simpleClassName, methodName, lineNumber);
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认值
            return "Unknown";
        }

        return "Unknown";
    }

    /**
     * 判断是否为框架类（需要跳过的类）
     */
    private boolean isFrameworkClass(String className) {
        if (className == null) {
            return true;
        }

        // 跳过的框架类包名
        String[] frameworkPackages = {
            "java.lang",
            "java.util",
            "java.sql",
            "javax.sql",
            "com.p6spy",
            "com.zaxxer.hikari",
            "org.apache.ibatis",
            "org.mybatis",
            "org.springframework.jdbc",
            "org.springframework.transaction",
            "org.springframework.aop",
            "org.springframework.cglib",
            "com.mysql.cj",
            "com.mysql.jdbc",
            "sun.reflect",
            "jdk.internal",
            "net.sf.cglib",
            "com.tuowan.yeliao.commons.config.p6spy",  // 跳过p6spy相关类
            "com.github.pagehelper",  // 跳过分页插件
            "com.baomidou.mybatisplus",  // 跳过MyBatis-Plus
            "com.easyooo.framework"  // 跳过easyooo框架
        };

        for (String pkg : frameworkPackages) {
            if (className.startsWith(pkg)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取简化的类名（去掉包名，只保留类名）
     */
    private String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) {
            return "Unknown";
        }

        int lastDot = fullClassName.lastIndexOf('.');
        if (lastDot >= 0 && lastDot < fullClassName.length() - 1) {
            return fullClassName.substring(lastDot + 1);
        }

        return fullClassName;
    }
}